"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { useLanguage } from "@/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import {
  ShoppingCart,
  Package,
  BarChart4,
  Settings,
  Tags,
  ClipboardList,
  <PERSON>u as MenuIcon,
  Home,
} from "lucide-react";

interface SalesMenuProps {
  className?: string;
}

export function SalesMenu({ className = "" }: SalesMenuProps) {
  const router = useRouter();
  const { t } = useLanguage();

  const menuItems = [
    {
      icon: <ShoppingCart className="h-6 w-6" />,
      label: t("layout.sales"),
      href: "/dashboard/sales",
      color: "bg-blue-100 text-blue-700",
    },
    {
      icon: <ClipboardList className="h-6 w-6" />,
      label: t("layout.orders"),
      href: "/dashboard/orders",
      color: "bg-amber-100 text-amber-700",
    },
    {
      icon: <Package className="h-6 w-6" />,
      label: t("layout.products"),
      href: "/dashboard/products",
      color: "bg-green-100 text-green-700",
    },
    {
      icon: <Tags className="h-6 w-6" />,
      label: t("layout.categories"),
      href: "/dashboard/categories",
      color: "bg-purple-100 text-purple-700",
    },
    {
      icon: <BarChart4 className="h-6 w-6" />,
      label: t("layout.reports"),
      href: "/dashboard/reports",
      color: "bg-orange-100 text-orange-700",
    },
    {
      icon: <Settings className="h-6 w-6" />,
      label: t("layout.settings"),
      href: "/dashboard/settings",
      color: "bg-slate-100 text-slate-700",
    },
    {
      icon: <MenuIcon className="h-6 w-6" />,
      label: t("layout.menu"),
      href: "/dashboard/menu",
      color: "bg-gray-100 text-gray-700",
    },
  ];

  return (
    <div className={`bg-background p-2 rounded-lg shadow-sm border ${className}`}>
      <div className="flex flex-wrap gap-2 justify-center">
        {menuItems.map((item) => (
          <Button
            key={item.href}
            variant="outline"
            className={`flex flex-col items-center justify-center h-16 w-16 p-1 ${item.color}`}
            onClick={() => router.push(item.href)}
          >
            {item.icon}
            <span className="text-xs mt-1 text-center line-clamp-1">{item.label}</span>
          </Button>
        ))}
      </div>
    </div>
  );
}
