"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { useLanguage } from "@/contexts/LanguageContext";
import { useStore } from "@/contexts/StoreContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useUnifiedAuth } from "@/hooks/useUnifiedAuth";
import { fetchProductsForSales, processCheckout } from "@/lib/sales";
import { toast } from "sonner";
import {
  Search,
  ShoppingCart,
  Plus,
  Minus,
  Trash2,
  Receipt as ReceiptIcon,
  Tags,
  Percent,
} from "lucide-react";
import Barcode from "react-barcode";

import { BarcodeListener } from "@/components/BarcodeListener";
import { BarcodeScanIndicator } from "@/components/BarcodeScanIndicator";
import { NumericKeypad } from "@/components/ui/numeric-keypad";
import { QuantityInputDialog } from "@/components/quantity-input-dialog";
import { QuickActions } from "@/components/quick-actions";
import { DiscountSelector } from "@/components/discount-selector";
import { ProductDiscountDialog } from "@/components/product-discount-dialog";
import { CustomerInfoDialog } from "@/components/customer-info-dialog";
import { Receipt } from "@/components/receipt";
import { SalesMenu } from "@/components/sales-menu";

type Product = {
  id: string;
  name: string;
  price: number;
  barcode: string | null;
  quantity: number;
  category_id: string | null;
  category_name?: string;
  imageUrl?: string;
};

type CartItem = {
  id: string;
  name: string;
  price: number;
  quantity: number;
  discount: number;
  discountType: "percentage" | "fixed" | "none";
  total: number;
  originalTotal: number;
  imageUrl?: string;
};

export default function SalesPage() {
  const { user, loading: authLoading } = useUnifiedAuth();
  const { t, formatCurrency } = useLanguage();
  const { storeName } = useStore();
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<{ id: string; name: string }[]>(
    []
  );
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [cart, setCart] = useState<CartItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCheckoutDialogOpen, setIsCheckoutDialogOpen] = useState(false);
  const [isReceiptDialogOpen, setIsReceiptDialogOpen] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState("cash");
  const [discountAmount, setDiscountAmount] = useState(0);
  const [amountReceived, setAmountReceived] = useState<number>(0);
  const [changeAmount, setChangeAmount] = useState<number>(0);
  const [receiptData, setReceiptData] = useState<any>(null);
  const [isQuantityDialogOpen, setIsQuantityDialogOpen] = useState(false);
  const [isProductDiscountDialogOpen, setIsProductDiscountDialogOpen] =
    useState(false);
  const [selectedCartItem, setSelectedCartItem] = useState<CartItem | null>(
    null
  );
  const [isDiscountKeypadOpen, setIsDiscountKeypadOpen] = useState(false);
  const [isDiscountPercentage, setIsDiscountPercentage] = useState(false);
  const [isAmountReceivedKeypadOpen, setIsAmountReceivedKeypadOpen] =
    useState(false);
  const [heldOrders, setHeldOrders] = useState<
    { id: string; items: CartItem[] }[]
  >([]);
  const [isCustomerInfoDialogOpen, setIsCustomerInfoDialogOpen] =
    useState(false);
  const [customerInfo, setCustomerInfo] = useState({
    name: "",
    phone: "",
    email: "",
  });
  const receiptRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  useEffect(() => {
    if (!authLoading) {
      fetchProducts();
    }
  }, [authLoading]);

  // Reset amount received and change when payment method changes
  useEffect(() => {
    if (paymentMethod !== "cash") {
      setAmountReceived(0);
      setChangeAmount(0);
    }
  }, [paymentMethod]);

  // Add keyboard shortcut to focus search field (Ctrl+F or Command+F)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Focus search field on Ctrl+F or Command+F
      if ((e.ctrlKey || e.metaKey) && e.key === "f") {
        e.preventDefault(); // Prevent browser's find dialog
        if (searchInputRef.current) {
          searchInputRef.current.focus();
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, []);

  useEffect(() => {
    let filtered = products;

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(
        (product) =>
          product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (product.barcode && product.barcode.includes(searchQuery))
      );
    }

    // Filter by category
    if (selectedCategory) {
      filtered = filtered.filter(
        (product) => product.category_id === selectedCategory
      );
    }

    setFilteredProducts(filtered);
  }, [searchQuery, selectedCategory, products]);

  async function fetchProducts() {
    setIsLoading(true);
    try {
      console.log("Fetching products for sales...");

      // Fetch products and categories from the local database
      const { products: fetchedProducts, categories: fetchedCategories } =
        await fetchProductsForSales();

      console.log("Products data received for sales:", fetchedProducts);
      console.log("Categories data received for sales:", fetchedCategories);

      // Update state with fetched data
      setProducts(fetchedProducts);
      setFilteredProducts(fetchedProducts);
      setCategories(fetchedCategories);
    } catch (error: any) {
      console.error("Error fetching products for sales:", error);
      // Show a more detailed error message
      toast.error(t("errors.failedToLoadProducts"), {
        description: error.message,
        id: "sales-products-error",
      });
      // Set empty products array to avoid undefined errors
      setProducts([]);
      setFilteredProducts([]);
      setCategories([]);
    } finally {
      setIsLoading(false);
    }
  }

  function addToCart(product: Product) {
    if (product.quantity <= 0) {
      toast.error(t("sales.outOfStock"));
      return;
    }

    setCart((prevCart) => {
      const existingItem = prevCart.find((item) => item.id === product.id);

      if (existingItem) {
        // Check if we have enough stock
        if (existingItem.quantity + 1 > product.quantity) {
          toast.error(t("sales.notEnoughStock"));
          return prevCart;
        }

        // Update quantity of existing item
        return prevCart.map((item) =>
          item.id === product.id
            ? {
                ...item,
                quantity: item.quantity + 1,
                originalTotal: (item.quantity + 1) * item.price,
                total: calculateItemTotal(
                  item.price,
                  item.quantity + 1,
                  item.discount,
                  item.discountType
                ),
              }
            : item
        );
      } else {
        // Add new item to cart
        const originalTotal = product.price;
        return [
          ...prevCart,
          {
            id: product.id,
            name: product.name,
            price: product.price,
            quantity: 1,
            discount: 0,
            discountType: "none",
            originalTotal: originalTotal,
            total: originalTotal,
            imageUrl: product.imageUrl,
          },
        ];
      }
    });
  }

  function removeFromCart(productId: string) {
    setCart((prevCart) => {
      const existingItem = prevCart.find((item) => item.id === productId);

      if (existingItem && existingItem.quantity > 1) {
        // Decrease quantity
        return prevCart.map((item) =>
          item.id === productId
            ? {
                ...item,
                quantity: item.quantity - 1,
                originalTotal: (item.quantity - 1) * item.price,
                total: calculateItemTotal(
                  item.price,
                  item.quantity - 1,
                  item.discount,
                  item.discountType
                ),
              }
            : item
        );
      } else {
        // Remove item from cart
        return prevCart.filter((item) => item.id !== productId);
      }
    });
  }

  function deleteFromCart(productId: string) {
    setCart((prevCart) => prevCart.filter((item) => item.id !== productId));
  }

  function handleQuantityChange(item: CartItem) {
    setSelectedCartItem(item);
    setIsQuantityDialogOpen(true);
  }

  function handleProductDiscount(item: CartItem) {
    setSelectedCartItem(item);
    setIsProductDiscountDialogOpen(true);
  }

  function applyProductDiscount(
    discount: number,
    discountType: "percentage" | "fixed" | "none"
  ) {
    if (!selectedCartItem) return;

    setCart((prevCart) =>
      prevCart.map((item) =>
        item.id === selectedCartItem.id
          ? {
              ...item,
              discount,
              discountType,
              total: calculateItemTotal(
                item.price,
                item.quantity,
                discount,
                discountType
              ),
            }
          : item
      )
    );
  }

  function updateCartItemQuantity(quantity: number) {
    if (!selectedCartItem) return;

    const product = products.find((p) => p.id === selectedCartItem.id);
    if (!product) return;

    // Check if we have enough stock
    if (quantity > product.quantity) {
      toast.error(t("sales.notEnoughStock"));
      return;
    }

    setCart((prevCart) =>
      prevCart.map((item) =>
        item.id === selectedCartItem.id
          ? {
              ...item,
              quantity,
              originalTotal: quantity * item.price,
              total: calculateItemTotal(
                item.price,
                quantity,
                item.discount,
                item.discountType
              ),
            }
          : item
      )
    );
  }

  function handleApplyDiscount() {
    setIsDiscountKeypadOpen(true);
  }

  function applyDiscount(amount: number, isPercentage: boolean) {
    setDiscountAmount(amount);
    setIsDiscountPercentage(isPercentage);
  }

  function handleCashPayment() {
    setPaymentMethod("cash");
    setIsAmountReceivedKeypadOpen(true);
  }

  function handleCardPayment() {
    setPaymentMethod("card");
    setIsCheckoutDialogOpen(true);
  }

  function handleMobilePayment() {
    setPaymentMethod("mobile");
    setIsCheckoutDialogOpen(true);
  }

  // Quick checkout functionality removed

  function handleHoldOrder() {
    if (cart.length === 0) return;

    // Generate a more readable order ID with a sequential number
    const orderNumber = heldOrders.length + 1;
    const orderId = `order-${orderNumber}-${Date.now().toString().slice(-4)}`;

    setHeldOrders((prev) => [...prev, { id: orderId, items: [...cart] }]);
    setCart([]);

    toast.success(t("sales.orderHeld"), {
      description: t("sales.orderHeldDescription", {
        id: orderId,
        count: heldOrders.length + 1,
      }),
    });
  }

  function handleRecallOrder() {
    if (heldOrders.length === 0) return;

    // If there's only one held order, recall it immediately
    if (heldOrders.length === 1) {
      const order = heldOrders[0];
      setCart(order.items);
      setHeldOrders((prev) => prev.filter((o) => o.id !== order.id));
      toast.success(t("sales.orderRecalled"), {
        description: t("sales.orderRecalledDescription", { id: order.id }),
      });
      return;
    }

    // If there are multiple held orders, show a dialog to select one
    // For now, we'll just recall the most recent order and show how many are available
    const lastOrder = heldOrders[heldOrders.length - 1];
    setCart(lastOrder.items);
    setHeldOrders((prev) => prev.filter((order) => order.id !== lastOrder.id));
    toast.success(t("sales.orderRecalled"), {
      description: t("sales.multipleOrdersAvailable", {
        id: lastOrder.id,
        count: heldOrders.length - 1,
      }),
    });
  }

  function handlePrintReceipt() {
    if (!receiptData) {
      toast.error(t("sales.noReceiptToPrint"));
      return;
    }

    handlePrint();
  }

  function handleSearchProducts() {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }

  function handleVoidLastItem() {
    if (cart.length === 0) return;

    const lastItem = cart[cart.length - 1];
    deleteFromCart(lastItem.id);
    toast.success(t("sales.itemVoided"), {
      description: t("sales.itemVoidedDescription", { name: lastItem.name }),
    });
  }

  function handleCustomerInfo() {
    setIsCustomerInfoDialogOpen(true);
  }

  // Calculate the total for an item with discount applied
  function calculateItemTotal(
    price: number,
    quantity: number,
    discount: number,
    discountType: "percentage" | "fixed" | "none"
  ): number {
    const originalTotal = price * quantity;

    if (discountType === "none" || discount <= 0) {
      return originalTotal;
    }

    if (discountType === "percentage") {
      return originalTotal - originalTotal * (discount / 100);
    }

    if (discountType === "fixed") {
      return Math.max(0, originalTotal - discount);
    }

    return originalTotal;
  }

  // Calculate cart totals
  const itemsSubtotal = cart.reduce((sum, item) => sum + item.originalTotal, 0);
  const itemsDiscountTotal = cart.reduce(
    (sum, item) => sum + (item.originalTotal - item.total),
    0
  );
  const subtotal = cart.reduce((sum, item) => sum + item.total, 0);
  const finalTotal = subtotal - discountAmount;

  async function handleCheckout() {
    if (cart.length === 0) {
      toast.error(t("sales.cartEmpty"));
      return;
    }

    if (!user) {
      console.error("User not authenticated");
      toast.error(t("sales.loginRequired"));
      router.push("/auth/login");
      return;
    }

    // For cash payments, if amount received is provided, validate that it's sufficient
    if (
      paymentMethod === "cash" &&
      amountReceived > 0 &&
      amountReceived < finalTotal
    ) {
      toast.error(t("sales.insufficientAmountReceived"));
      return;
    }

    try {
      console.log("Processing checkout...");

      // Validate cart items
      if (!cart || cart.length === 0) {
        toast.error(t("sales.cartEmpty"));
        return;
      }

      // Validate cart items have required properties
      for (const item of cart) {
        if (!item.id) {
          console.error("Missing id in cart item:", item);
          toast.error("Invalid cart item: Missing product ID");
          return;
        }
        if (item.quantity === undefined || item.quantity <= 0) {
          console.error("Invalid quantity in cart item:", item);
          toast.error("Invalid cart item: Invalid quantity");
          return;
        }
        if (item.price === undefined || isNaN(item.price)) {
          console.error("Invalid price in cart item:", item);
          toast.error("Invalid cart item: Invalid price");
          return;
        }
        if (item.total === undefined || isNaN(item.total)) {
          console.error("Invalid total in cart item:", item);
          toast.error("Invalid cart item: Invalid total");
          return;
        }

        // Ensure discount and discountType are set
        if (item.discount === undefined) {
          console.log("Setting default discount for item:", item.id);
          item.discount = 0;
        }
        if (!item.discountType) {
          console.log("Setting default discountType for item:", item.id);
          item.discountType = "none";
        }
      }

      // Process the checkout using the local database
      console.log("Creating transaction with data:", {
        user_id: user.id,
        total_amount: subtotal,
        discount_amount: discountAmount,
        final_amount: finalTotal,
        payment_method: paymentMethod,
        cart_items: cart.length,
      });

      const { transactionId } = await processCheckout(
        user.id,
        cart,
        subtotal,
        discountAmount,
        finalTotal,
        paymentMethod,
        customerInfo.name || customerInfo.phone || customerInfo.email
          ? customerInfo
          : undefined
      );

      console.log("Transaction created with ID:", transactionId);

      // Prepare receipt data
      setReceiptData({
        id: transactionId,
        date: new Date(),
        items: cart,
        subtotal: itemsSubtotal,
        itemsDiscountTotal,
        discount: discountAmount,
        total: finalTotal,
        paymentMethod,
        amountReceived:
          paymentMethod === "cash" && amountReceived > 0
            ? amountReceived
            : finalTotal,
        change:
          paymentMethod === "cash" && amountReceived > 0 ? changeAmount : 0,
        customerInfo:
          customerInfo.name || customerInfo.phone || customerInfo.email
            ? customerInfo
            : undefined,
        cashierName:
          user.firstName && user.lastName
            ? `${user.firstName} ${user.lastName}`
            : user.firstName || user.email,
      });

      // Show receipt
      setIsCheckoutDialogOpen(false);
      setIsReceiptDialogOpen(true);

      // Reset cart and refresh products
      setCart([]);
      setDiscountAmount(0);
      setAmountReceived(0);
      setChangeAmount(0);
      fetchProducts();

      toast.success(t("sales.transactionSuccess"));
    } catch (error: any) {
      console.error("Checkout error:", error);

      // Log detailed error information
      console.error("Error details:", {
        message: error.message,
        stack: error.stack,
        name: error.name,
        cause: error.cause,
      });

      // Show a more detailed error message to the user
      toast.error(t("sales.transactionError"), {
        description: `Error: ${error.message}`,
        id: "checkout-error",
      });

      // Keep the checkout dialog open so the user can try again
      setIsCheckoutDialogOpen(true);
    }
  }

  const [receiptWidth, setReceiptWidth] = useState<"58mm" | "80mm">("80mm");

  const handlePrint = () => {
    if (!receiptRef.current) {
      console.error("Receipt ref is not available");
      toast.error(t("sales.printError"), {
        description: t("errors.somethingWentWrong"),
      });
      return;
    }

    try {
      // Create a new window for printing
      const printWindow = window.open("", "_blank");
      if (!printWindow) {
        toast.error(t("sales.printError"), {
          description: t("errors.popupBlocked"),
        });
        return;
      }

      // Get the HTML content of the receipt
      const receiptContent = receiptRef.current.innerHTML;

      // Calculate paper width in pixels (approximate)
      const paperWidth = receiptWidth === "58mm" ? "210px" : "300px";

      // Write the content to the new window
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>${storeName} - Receipt</title>
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            /* Reset and base styles */
            * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
            }

            body {
              font-family: 'Courier New', monospace;
              font-size: ${receiptWidth === "58mm" ? "10px" : "12px"};
              line-height: 1.2;
              width: ${paperWidth};
              margin: 0 auto;
              padding: 5px;
              background-color: white;
              color: black;
            }

            /* Receipt container */
            .receipt {
              width: 100%;
            }

            /* Header */
            .receipt-header {
              text-align: center;
              margin-bottom: 10px;
              padding-bottom: 5px;
              border-bottom: 1px dashed #000;
            }

            .receipt-header h2 {
              font-size: ${receiptWidth === "58mm" ? "12px" : "14px"};
              font-weight: bold;
              margin-bottom: 3px;
            }

            .receipt-header p {
              font-size: ${receiptWidth === "58mm" ? "9px" : "10px"};
              margin-bottom: 2px;
            }

            /* Items table */
            table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 10px;
            }

            th, td {
              text-align: left;
              padding: 2px 0;
            }

            th {
              font-weight: bold;
              border-bottom: 1px solid #000;
            }

            .text-right {
              text-align: right;
            }

            /* Totals section */
            .totals {
              border-top: 1px dashed #000;
              padding-top: 5px;
              margin-bottom: 10px;
            }

            .totals div {
              display: flex;
              justify-content: space-between;
              margin-bottom: 2px;
            }

            .font-bold {
              font-weight: bold;
            }

            /* Footer */
            .receipt-footer {
              text-align: center;
              border-top: 1px dashed #000;
              padding-top: 5px;
              font-size: ${receiptWidth === "58mm" ? "8px" : "9px"};
            }

            /* Print-specific styles */
            @media print {
              @page {
                size: ${receiptWidth} auto;
                margin: 0;
              }

              body {
                width: ${paperWidth};
                margin: 0;
                padding: 0;
              }

              .receipt {
                width: 100%;
              }

              /* Hide any browser UI elements */
              html, body {
                background-color: white;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
              }
            }
          </style>
        </head>
        <body>
          <div class="receipt">
            ${receiptContent}
          </div>
          <script>
            window.onload = function() {
              window.print();
              window.onafterprint = function() {
                window.close();
              };
            };
          </script>
        </body>
        </html>
      `);

      printWindow.document.close();
    } catch (error) {
      console.error("Print error:", error);
      toast.error(t("sales.printError"), {
        description:
          error instanceof Error
            ? error.message
            : t("errors.somethingWentWrong"),
      });
    }
  };

  function handleBarcodeInput(e: React.KeyboardEvent<HTMLInputElement>) {
    if (e.key === "Enter") {
      processBarcode((e.target as HTMLInputElement).value);
    }
  }

  // Function to process barcode from any source (input field or global scanner)
  function processBarcode(barcode: string) {
    if (!barcode) return;

    console.log("Processing barcode:", barcode);
    const product = products.find((p) => p.barcode === barcode);

    if (product) {
      addToCart(product);
      setSearchQuery("");
      toast.success(t("sales.added", { name: product.name }));
    } else {
      toast.error(t("sales.productNotFound"));
    }
  }

  return (
    <div className="flex flex-col gap-4">
      {/* Global barcode listener */}
      <BarcodeListener onBarcodeScanned={processBarcode} />

      {/* Visual indicator when scanning */}
      <BarcodeScanIndicator />

      {/* Persistent Menu Bar */}
      <SalesMenu className="sticky top-[4.5rem] z-20" />

      <div className="flex flex-col lg:flex-row gap-4">
        {/* Product List */}
        <div className="lg:w-3/5 space-y-4">
          <div>
            <h1 className="text-3xl font-bold">{t("sales.salesTerminal")}</h1>
            <p className="text-muted-foreground">{t("sales.processSales")}</p>
          </div>

          <div className="flex flex-col gap-4">
            <div className="flex items-center gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-3.5 h-5 w-5 text-muted-foreground" />
                <Input
                  ref={searchInputRef}
                  type="search"
                  placeholder={t("sales.searchProducts")}
                  className="h-10 pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={handleBarcodeInput}
                  autoFocus
                />
              </div>
            </div>

            {/* Category Filter */}
            {categories.length > 0 && (
              <div className="flex flex-wrap gap-2">
                <Button
                  variant={selectedCategory === null ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(null)}
                  className="flex h-8 items-center gap-1 text-sm"
                >
                  <Tags className="h-4 w-4" />
                  {t("sales.allCategories")}
                </Button>

                {categories.map((category) => (
                  <Button
                    key={category.id}
                    variant={
                      selectedCategory === category.id ? "default" : "outline"
                    }
                    size="sm"
                    className="h-8 text-sm"
                    onClick={() => setSelectedCategory(category.id)}
                  >
                    {category.name}
                  </Button>
                ))}
              </div>
            )}
          </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-3">
          {isLoading ? (
            <p>{t("products.loadingProducts")}</p>
          ) : filteredProducts.length === 0 ? (
            <p>{t("products.noProductsFound")}</p>
          ) : (
            filteredProducts.map((product) => (
              <Card
                key={product.id}
                className="overflow-hidden cursor-pointer hover:shadow-md transition-all flex flex-col"
                onClick={() => product.quantity > 0 && addToCart(product)}
              >
                <div className="relative h-44 overflow-hidden">
                  <img
                    src={
                      product.imageUrl ||
                      `https://placehold.co/400x400/e2e8f0/1e293b?text=${t(
                        "products.product"
                      )}`
                    }
                    alt={product.name}
                    className="w-full h-full object-cover bg-gray-50"
                    onError={(e) => {
                      e.currentTarget.src = `https://placehold.co/400x400/e2e8f0/1e293b?text=${t(
                        "products.product"
                      )}`;
                    }}
                  />
                </div>
                <CardHeader className="p-3">
                  <CardTitle className="text-lg line-clamp-1">
                    {product.name}
                  </CardTitle>
                  <div className="flex justify-between items-center">
                    <CardDescription className="text-lg font-semibold">
                      {formatCurrency(product.price)}
                    </CardDescription>
                    {product.category_name &&
                      product.category_name !== "Uncategorized" && (
                        <div className="text-xs inline-flex items-center px-1.5 py-0.5 rounded-full bg-muted">
                          <Tags className="h-3 w-3 mr-0.5" />
                          <span className="max-w-[80px] truncate">
                            {product.category_name}
                          </span>
                        </div>
                      )}
                  </div>
                </CardHeader>
                <CardFooter className="p-3 pt-0 flex justify-between items-center mt-auto gap-2">
                  <div className="text-sm">
                    {product.quantity === 0 ? (
                      <span className="text-red-600 flex items-center">
                        <span className="inline-block w-2.5 h-2.5 rounded-full bg-red-500 mr-1"></span>
                        {t("products.outOfStock")}
                      </span>
                    ) : product.quantity <= 5 ? (
                      <span className="text-amber-600 flex items-center">
                        <span className="inline-block w-2.5 h-2.5 rounded-full bg-amber-500 mr-1"></span>
                        {product.quantity}
                      </span>
                    ) : (
                      <span className="text-green-600 flex items-center">
                        <span className="inline-block w-2.5 h-2.5 rounded-full bg-green-500 mr-1"></span>
                        {product.quantity}
                      </span>
                    )}
                  </div>
                  <Button
                    size="icon"
                    variant="secondary"
                    className="h-8 w-8"
                    onClick={(e) => {
                      e.stopPropagation();
                      addToCart(product);
                    }}
                    disabled={product.quantity <= 0}
                  >
                    <Plus className="h-5 w-5" />
                    <span className="sr-only">{t("products.add")}</span>
                  </Button>
                </CardFooter>
              </Card>
            ))
          )}
        </div>
        </div>

      {/* Cart */}
      <div className="lg:w-2/5">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ShoppingCart className="h-5 w-5 mr-2" />
              {t("sales.shoppingCart")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {cart.length === 0 ? (
              <div className="text-center py-6">
                <ShoppingCart className="h-12 w-12 mx-auto text-muted-foreground" />
                <p className="mt-2 text-muted-foreground">
                  {t("sales.emptyCart")}
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="overflow-x-auto">
                  <Table className="w-full">
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[50%] text-base">
                          {t("sales.item")}
                        </TableHead>
                        <TableHead className="text-right w-[12%] text-base">
                          {t("sales.qty")}
                        </TableHead>
                        <TableHead className="text-right w-[18%] text-base">
                          {t("sales.price")}
                        </TableHead>
                        <TableHead className="text-right w-[20%] text-base">
                          {t("sales.actions")}
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {cart.map((item) => (
                        <TableRow key={item.id}>
                          <TableCell
                            className="font-medium p-3 text-base cursor-pointer"
                            onClick={() => handleQuantityChange(item)}
                          >
                            <div className="flex items-center gap-2">
                              <div className="w-10 h-10 relative rounded overflow-hidden bg-gray-50 flex-shrink-0">
                                <img
                                  src={
                                    item.imageUrl ||
                                    `https://placehold.co/400x400/e2e8f0/1e293b?text=${t(
                                      "products.product"
                                    )}`
                                  }
                                  alt={item.name}
                                  className="object-cover w-full h-full"
                                  onError={(e) => {
                                    e.currentTarget.src = `https://placehold.co/400x400/e2e8f0/1e293b?text=${t(
                                      "products.product"
                                    )}`;
                                  }}
                                />
                              </div>
                              <span>{item.name}</span>
                            </div>
                          </TableCell>
                          <TableCell
                            className="text-right p-3 text-base cursor-pointer"
                            onClick={() => handleQuantityChange(item)}
                          >
                            {item.quantity}
                          </TableCell>
                          <TableCell className="text-right p-3 text-base">
                            {item.discountType !== "none" ? (
                              <div>
                                <span className="line-through text-muted-foreground text-sm">
                                  {formatCurrency(item.originalTotal)}
                                </span>
                                <div className="text-green-600">
                                  {formatCurrency(item.total)}
                                  {item.discountType === "percentage" && (
                                    <span className="text-xs ml-1">
                                      (-{item.discount}%)
                                    </span>
                                  )}
                                </div>
                              </div>
                            ) : (
                              formatCurrency(item.total)
                            )}
                          </TableCell>
                          <TableCell className="text-right p-1">
                            <div className="flex justify-end gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                className={`h-9 w-9 p-0 ${
                                  item.discountType !== "none"
                                    ? "text-green-600"
                                    : ""
                                }`}
                                onClick={() => handleProductDiscount(item)}
                              >
                                <Percent className="h-5 w-5" />
                                <span className="sr-only">
                                  {t("sales.discount")}
                                </span>
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-9 w-9 p-0"
                                onClick={() =>
                                  addToCart(
                                    products.find((p) => p.id === item.id)!
                                  )
                                }
                              >
                                <Plus className="h-5 w-5" />
                                <span className="sr-only">
                                  {t("products.add")}
                                </span>
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-9 w-9 p-0"
                                onClick={() => removeFromCart(item.id)}
                              >
                                <Minus className="h-5 w-5" />
                                <span className="sr-only">
                                  {t("sales.remove")}
                                </span>
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-9 w-9 p-0"
                                onClick={() => deleteFromCart(item.id)}
                              >
                                <Trash2 className="h-5 w-5" />
                                <span className="sr-only">
                                  {t("products.delete")}
                                </span>
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                <div className="pt-4 space-y-2">
                  <div className="flex justify-between text-xl">
                    <span>{t("sales.subtotal")}</span>
                    <span>{formatCurrency(itemsSubtotal)}</span>
                  </div>

                  {itemsDiscountTotal > 0 && (
                    <div className="flex justify-between text-xl text-green-600">
                      <span>{t("sales.itemDiscounts")}</span>
                      <span>-{formatCurrency(itemsDiscountTotal)}</span>
                    </div>
                  )}

                  {discountAmount > 0 && (
                    <div className="flex justify-between text-xl">
                      <span>
                        {t("sales.orderDiscount")}
                        {isDiscountPercentage
                          ? ` (${((discountAmount / subtotal) * 100).toFixed(
                              1
                            )}%)`
                          : ""}
                      </span>
                      <span>-{formatCurrency(discountAmount)}</span>
                    </div>
                  )}

                  <div className="flex justify-between font-bold text-2xl">
                    <span>{t("sales.total")}</span>
                    <span>{formatCurrency(finalTotal)}</span>
                  </div>
                </div>

                {/* Quick Actions */}
                <QuickActions
                  onClearCart={() => setCart([])}
                  onApplyDiscount={handleApplyDiscount}
                  onCashPayment={handleCashPayment}
                  onCardPayment={handleCardPayment}
                  onMobilePayment={handleMobilePayment}
                  onQuantityChange={() => {
                    // If there's an item in the cart, select the first one
                    if (cart.length > 0) {
                      handleQuantityChange(cart[0]);
                    }
                  }}
                  onHoldOrder={handleHoldOrder}
                  onRecallOrder={handleRecallOrder}
                  onPrintReceipt={handlePrintReceipt}
                  onSearchProducts={handleSearchProducts}
                  onVoidLastItem={handleVoidLastItem}
                  onCustomerInfo={handleCustomerInfo}
                  onOrderHistory={() => router.push("/dashboard/orders")}
                  cartEmpty={cart.length === 0}
                  hasHeldOrders={heldOrders.length > 0}
                  hasLastReceipt={receiptData !== null}
                />
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              variant="outline"
              size="lg"
              className="h-16 text-lg"
              onClick={() => setCart([])}
            >
              {t("sales.clearCart")}
            </Button>
            <Dialog
              open={isCheckoutDialogOpen}
              onOpenChange={setIsCheckoutDialogOpen}
            >
              <DialogTrigger asChild>
                <Button
                  size="lg"
                  className="h-16 text-lg"
                  disabled={cart.length === 0}
                >
                  {t("sales.checkout")}
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>{t("sales.checkout")}</DialogTitle>
                  <DialogDescription>
                    {t("sales.completeTransaction")}
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <label className="text-base font-medium">
                      {t("sales.paymentMethod")}
                    </label>
                    <Select
                      value={paymentMethod}
                      onValueChange={setPaymentMethod}
                    >
                      <SelectTrigger className="h-12 text-base">
                        <SelectValue placeholder={t("sales.paymentMethod")} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cash" className="text-base">
                          {t("sales.cash")}
                        </SelectItem>
                        <SelectItem value="card" className="text-base">
                          {t("sales.card")}
                        </SelectItem>
                        <SelectItem value="mobile" className="text-base">
                          {t("sales.mobilePayment")}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  {/* Discount field removed from checkout dialog */}

                  {paymentMethod === "cash" && (
                    <div className="space-y-2">
                      <label className="text-base font-medium">
                        {t("sales.amountReceived")}
                      </label>
                      <div className="relative">
                        <Input
                          type="text"
                          inputMode="none"
                          className="h-12 text-base text-right pr-10"
                          value={amountReceived || ""}
                          onClick={() => setIsAmountReceivedKeypadOpen(true)}
                          readOnly
                        />
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                          {t("common.currency")}
                        </div>
                      </div>

                      {isAmountReceivedKeypadOpen && (
                        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center">
                          <div className="bg-background rounded-lg shadow-lg max-w-md w-full">
                            <div className="p-4">
                              <h3 className="text-lg font-medium mb-4">
                                {t("sales.amountReceived")}
                              </h3>
                              <NumericKeypad
                                value={amountReceived.toString()}
                                onChange={(value) => {
                                  const numValue = parseFloat(value);
                                  setAmountReceived(
                                    isNaN(numValue) ? 0 : numValue
                                  );
                                  setChangeAmount(
                                    isNaN(numValue)
                                      ? 0
                                      : Math.max(0, numValue - finalTotal)
                                  );
                                }}
                                onClose={() =>
                                  setIsAmountReceivedKeypadOpen(false)
                                }
                                allowDecimal={true}
                                currency={t("common.currency")}
                                minValue={0}
                              />
                              <div className="mt-4 flex justify-between">
                                <Button
                                  variant="outline"
                                  size="lg"
                                  className="h-12"
                                  onClick={() => {
                                    setAmountReceived(0);
                                    setChangeAmount(0);
                                    setIsAmountReceivedKeypadOpen(false);
                                  }}
                                >
                                  {t("sales.skip")}
                                </Button>
                                <Button
                                  size="lg"
                                  className="h-12"
                                  onClick={() =>
                                    setIsAmountReceivedKeypadOpen(false)
                                  }
                                >
                                  {t("products.confirm")}
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  <div className="space-y-2 pt-2">
                    <div className="flex justify-between">
                      <span>{t("sales.subtotal")}</span>
                      <span>{formatCurrency(itemsSubtotal)}</span>
                    </div>

                    {itemsDiscountTotal > 0 && (
                      <div className="flex justify-between text-green-600">
                        <span>{t("sales.itemDiscounts")}</span>
                        <span>-{formatCurrency(itemsDiscountTotal)}</span>
                      </div>
                    )}

                    {discountAmount > 0 && (
                      <div className="flex justify-between">
                        <span>{t("sales.orderDiscount")}</span>
                        <span>-{formatCurrency(discountAmount)}</span>
                      </div>
                    )}

                    <div className="flex justify-between font-bold">
                      <span>{t("sales.total")}</span>
                      <span>{formatCurrency(finalTotal)}</span>
                    </div>

                    {paymentMethod === "cash" &&
                      amountReceived >= finalTotal && (
                        <div className="flex justify-between font-bold text-green-600 pt-2">
                          <span>{t("sales.change")}</span>
                          <span>{formatCurrency(changeAmount)}</span>
                        </div>
                      )}
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    size="lg"
                    className="h-12 text-base"
                    onClick={() => setIsCheckoutDialogOpen(false)}
                  >
                    {t("products.cancel")}
                  </Button>
                  <Button
                    size="lg"
                    className="h-12 text-base"
                    onClick={handleCheckout}
                  >
                    {t("sales.completeSale")}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </CardFooter>
        </Card>
      </div>

      {/* Quantity Input Dialog */}
      <QuantityInputDialog
        isOpen={isQuantityDialogOpen}
        onClose={() => setIsQuantityDialogOpen(false)}
        onConfirm={updateCartItemQuantity}
        initialQuantity={selectedCartItem?.quantity || 1}
        maxQuantity={
          selectedCartItem
            ? products.find((p) => p.id === selectedCartItem.id)?.quantity
            : undefined
        }
        productName={selectedCartItem?.name}
      />

      {/* Standalone Discount Selector */}
      {isDiscountKeypadOpen && (
        <DiscountSelector
          onClose={() => setIsDiscountKeypadOpen(false)}
          onApplyDiscount={applyDiscount}
          subtotal={subtotal}
          currentDiscount={discountAmount}
        />
      )}

      {/* Product Discount Dialog */}
      {isProductDiscountDialogOpen && selectedCartItem && (
        <ProductDiscountDialog
          isOpen={isProductDiscountDialogOpen}
          onClose={() => setIsProductDiscountDialogOpen(false)}
          onApplyDiscount={applyProductDiscount}
          productName={selectedCartItem.name}
          productPrice={selectedCartItem.price}
          productQuantity={selectedCartItem.quantity}
          currentDiscount={selectedCartItem.discount}
          currentDiscountType={selectedCartItem.discountType}
        />
      )}

      {/* Customer Info Dialog */}
      <CustomerInfoDialog
        isOpen={isCustomerInfoDialogOpen}
        onClose={() => setIsCustomerInfoDialogOpen(false)}
        onSave={(info) => {
          setCustomerInfo(info);
          toast.success(t("sales.customerInfoSaved"));
        }}
        initialValues={customerInfo}
      />

      {/* Receipt Dialog */}
      <Dialog open={isReceiptDialogOpen} onOpenChange={setIsReceiptDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{t("sales.receipt")}</DialogTitle>
            <DialogDescription>
              {t("sales.transactionSuccess")}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {/* Paper size selector */}
            <div className="mb-4 flex items-center justify-between">
              <label className="text-sm font-medium">
                {t("sales.paperSize")}:
              </label>
              <div className="flex gap-2">
                <Button
                  variant={receiptWidth === "58mm" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setReceiptWidth("58mm")}
                >
                  58mm
                </Button>
                <Button
                  variant={receiptWidth === "80mm" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setReceiptWidth("80mm")}
                >
                  80mm
                </Button>
              </div>
            </div>

            {/* Receipt preview */}
            <div ref={receiptRef}>
              <Receipt
                receiptData={receiptData}
                width={receiptWidth}
                storeName={storeName}
              />
            </div>
          </div>
          <DialogFooter className="flex flex-col sm:flex-row gap-2">
            <div className="flex items-center gap-2 w-full sm:w-auto">
              <Select
                value={receiptWidth}
                onValueChange={(value: "58mm" | "80mm") =>
                  setReceiptWidth(value)
                }
              >
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder={t("sales.paperSize")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="58mm">58mm</SelectItem>
                  <SelectItem value="80mm">80mm</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex gap-2 w-full sm:w-auto">
              <Button
                variant="outline"
                onClick={() => setIsReceiptDialogOpen(false)}
                className="flex-1 sm:flex-none"
              >
                {t("sales.close")}
              </Button>
              <Button onClick={handlePrint} className="flex-1 sm:flex-none">
                <ReceiptIcon className="h-4 w-4 mr-2" />
                {t("sales.printReceipt")}
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
