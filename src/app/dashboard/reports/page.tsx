"use client";

import { useState, useEffect, useRef } from "react";
import { useLanguage } from "@/contexts/LanguageContext";
import { useStore } from "@/contexts/StoreContext";
import { clientDb } from "@/lib/database/client";
import { toast } from "sonner";
import {
  format,
  subDays,
  startOfMonth,
  endOfMonth,
  parseISO,
  eachDayOfInterval,
} from "date-fns";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip as ChartTooltip,
  Legend as ChartLegend,
  Filler,
  ChartOptions,
  ChartData,
} from "chart.js";
import { Line, Bar, Pie, Doughnut } from "react-chartjs-2";
import ChartDataLabels from "chartjs-plugin-datalabels";
import {
  Calendar,
  Download,
  Printer,
  RefreshCw,
  TrendingUp,
  Package,
  ShoppingCart,
  DollarSign,
  CreditCard,
  Wallet,
} from "lucide-react";

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  ChartTooltip,
  ChartLegend,
  Filler,
  ChartDataLabels
);

// Define types for our data
type DateRange = "today" | "yesterday" | "week" | "month" | "custom";
type ReportType = "sales" | "inventory" | "transactions";
type SalesData = {
  date: string;
  amount: number;
  transactions: number;
  profit: number;
};
type CategorySalesData = {
  category: string;
  amount: number;
  count: number;
};
type PaymentMethodData = {
  method: string;
  amount: number;
  count: number;
};
type TopProductData = {
  product: string;
  quantity: number;
  amount: number;
  profit: number;
  profitMargin: number;
};
type TransactionData = {
  id: string;
  date: string;
  amount: number;
  payment_method: string;
  status: string;
};

export default function ReportsPage() {
  const { t, formatCurrency } = useLanguage();
  const { storeName } = useStore();
  const [reportType, setReportType] = useState<ReportType>("sales");
  const [dateRange, setDateRange] = useState<DateRange>("month");
  const [startDate, setStartDate] = useState<string>(
    format(startOfMonth(new Date()), "yyyy-MM-dd")
  );
  const [endDate, setEndDate] = useState<string>(
    format(endOfMonth(new Date()), "yyyy-MM-dd")
  );
  const [isLoading, setIsLoading] = useState(true);
  const [salesData, setSalesData] = useState<SalesData[]>([]);
  const [categorySalesData, setCategorySalesData] = useState<
    CategorySalesData[]
  >([]);
  const [paymentMethodData, setPaymentMethodData] = useState<
    PaymentMethodData[]
  >([]);
  const [topProductsData, setTopProductsData] = useState<TopProductData[]>([]);
  const [transactionsData, setTransactionsData] = useState<TransactionData[]>(
    []
  );
  const [totalSales, setTotalSales] = useState(0);
  const [totalProfit, setTotalProfit] = useState(0);
  const [profitMargin, setProfitMargin] = useState(0);
  const [totalTransactions, setTotalTransactions] = useState(0);
  const [averageOrderValue, setAverageOrderValue] = useState(0);

  // Chart.js color palette
  const CHART_COLORS = {
    primary: "rgba(99, 102, 241, 1)", // indigo
    primaryLight: "rgba(99, 102, 241, 0.2)",
    secondary: "rgba(20, 184, 166, 1)", // teal
    secondaryLight: "rgba(20, 184, 166, 0.2)",
    success: "rgba(34, 197, 94, 1)", // green
    successLight: "rgba(34, 197, 94, 0.2)",
    warning: "rgba(245, 158, 11, 1)", // amber
    warningLight: "rgba(245, 158, 11, 0.2)",
    info: "rgba(6, 182, 212, 1)", // cyan
    infoLight: "rgba(6, 182, 212, 0.2)",
    purple: "rgba(139, 92, 246, 1)", // violet
    purpleLight: "rgba(139, 92, 246, 0.2)",
    pink: "rgba(236, 72, 153, 1)", // pink
    pinkLight: "rgba(236, 72, 153, 0.2)",
    red: "rgba(244, 63, 94, 1)", // rose
    redLight: "rgba(244, 63, 94, 0.2)",
  };

  // Chart.js global options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    animation: {
      duration: 1000,
      easing: "easeOutQuart",
    },
    plugins: {
      legend: {
        position: "top" as const,
        labels: {
          font: {
            size: 12,
            family: "system-ui, sans-serif",
          },
          usePointStyle: true,
          padding: 20,
        },
      },
      tooltip: {
        backgroundColor: "rgba(255, 255, 255, 0.9)",
        titleColor: "#000",
        bodyColor: "#000",
        borderColor: "rgba(0, 0, 0, 0.1)",
        borderWidth: 1,
        padding: 12,
        cornerRadius: 6,
        boxPadding: 6,
        usePointStyle: true,
        callbacks: {
          labelColor: function (context: any) {
            const colorIndex =
              context.dataIndex % Object.keys(CHART_COLORS).length;
            const color = Object.values(CHART_COLORS)[colorIndex];
            return {
              backgroundColor: color,
              borderColor: color,
            };
          },
        },
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          font: {
            size: 12,
          },
        },
      },
      y: {
        grid: {
          borderDash: [3, 3],
          color: "rgba(0, 0, 0, 0.1)",
        },
        ticks: {
          font: {
            size: 12,
          },
          callback: function (value: any) {
            if (value >= 1000) {
              return (value / 1000).toFixed(1) + "k";
            }
            return value;
          },
        },
      },
    },
    elements: {
      line: {
        tension: 0.4, // Smooth curves
      },
      point: {
        radius: 4,
        hoverRadius: 6,
      },
      bar: {
        borderRadius: 4,
      },
    },
  };

  useEffect(() => {
    updateDateRange(dateRange);
  }, [dateRange]);

  useEffect(() => {
    fetchReportData();
  }, [reportType, startDate, endDate]);

  const updateDateRange = (range: DateRange) => {
    const today = new Date();

    switch (range) {
      case "today":
        setStartDate(format(today, "yyyy-MM-dd"));
        setEndDate(format(today, "yyyy-MM-dd"));
        break;
      case "yesterday":
        const yesterday = subDays(today, 1);
        setStartDate(format(yesterday, "yyyy-MM-dd"));
        setEndDate(format(yesterday, "yyyy-MM-dd"));
        break;
      case "week":
        setStartDate(format(subDays(today, 7), "yyyy-MM-dd"));
        setEndDate(format(today, "yyyy-MM-dd"));
        break;
      case "month":
        setStartDate(format(startOfMonth(today), "yyyy-MM-dd"));
        setEndDate(format(endOfMonth(today), "yyyy-MM-dd"));
        break;
      case "custom":
        // Keep the current custom dates
        break;
    }
  };

  const fetchReportData = async () => {
    setIsLoading(true);
    try {
      // Fetch data based on report type
      if (reportType === "sales") {
        await fetchSalesReport();
      } else if (reportType === "inventory") {
        await fetchInventoryReport();
      } else if (reportType === "transactions") {
        await fetchTransactionsReport();
      }
    } catch (error: any) {
      console.error("Error fetching report data:", error);
      toast.error(t("errors.somethingWentWrong"), {
        description: error.message || t("errors.databaseError"),
        id: "reports-error",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchSalesReport = async () => {
    try {
      // Fetch transactions for the date range
      const startDateTime = new Date(`${startDate}T00:00:00`);
      const endDateTime = new Date(`${endDate}T23:59:59`);

      const transactions = await clientDb.transactions.findByDateRange(
        startDateTime,
        endDateTime
      );

      // Process transactions for sales over time
      const salesByDate = new Map<
        string,
        { amount: number; transactions: number; profit: number }
      >();
      let totalAmount = 0;
      let totalProfitAmount = 0;
      let transactionCount = 0;

      // Process each transaction
      for (const transaction of transactions) {
        const date = format(new Date(transaction.createdAt), "yyyy-MM-dd");
        const existing = salesByDate.get(date) || {
          amount: 0,
          transactions: 0,
          profit: 0,
        };

        // Calculate profit for this transaction
        let transactionProfit = 0;
        if (transaction.items && Array.isArray(transaction.items)) {
          for (const item of transaction.items) {
            // Calculate profit for each item
            const sellingPrice = item.unitPrice || 0;
            const purchasePrice = item.product?.purchasePrice || 0;
            const quantity = item.quantity || 0;
            const itemProfit = (sellingPrice - purchasePrice) * quantity;
            transactionProfit += itemProfit;
          }
        }

        // Update sales by date
        salesByDate.set(date, {
          amount: existing.amount + (transaction.finalAmount || 0),
          transactions: existing.transactions + 1,
          profit: existing.profit + transactionProfit,
        });

        // Update totals
        totalAmount += transaction.finalAmount || 0;
        totalProfitAmount += transactionProfit;
        transactionCount += 1;
      }

      // Convert to array for chart
      const salesDataArray = Array.from(salesByDate.entries()).map(
        ([date, data]) => ({
          date,
          amount: data.amount,
          transactions: data.transactions,
          profit: data.profit,
        })
      );

      // Calculate profit margin
      const profitMarginValue =
        totalAmount > 0 ? (totalProfitAmount / totalAmount) * 100 : 0;

      // Update state
      setSalesData(salesDataArray);
      setTotalSales(totalAmount);
      setTotalProfit(totalProfitAmount);
      setProfitMargin(profitMarginValue);
      setTotalTransactions(transactionCount);
      setAverageOrderValue(
        transactionCount > 0 ? totalAmount / transactionCount : 0
      );

      // Fetch category sales data
      await fetchCategorySales();

      // Fetch payment method data
      await fetchPaymentMethodData();

      // Fetch top products
      await fetchTopProducts();
    } catch (error) {
      console.error("Error fetching sales report:", error);
      throw error;
    }
  };

  const fetchCategorySales = async () => {
    try {
      const startDateTime = new Date(`${startDate}T00:00:00`);
      const endDateTime = new Date(`${endDate}T23:59:59`);

      // Get sales by category using the client DB
      const categorySalesData = await clientDb.transactions.getSalesByCategory(
        startDateTime,
        endDateTime
      );

      if (!categorySalesData || !Array.isArray(categorySalesData)) {
        console.warn("No category sales data returned");
        setCategorySalesData([]);
        return;
      }

      // Process data by category
      const categorySales = new Map<
        string,
        { amount: number; count: number }
      >();

      categorySalesData.forEach((item: any) => {
        const categoryName = item.categoryName || "Uncategorized";
        const existing = categorySales.get(categoryName) || {
          amount: 0,
          count: 0,
        };

        categorySales.set(categoryName, {
          amount: existing.amount + (item.totalAmount || 0),
          count: existing.count + (item.itemCount || 0),
        });
      });

      // Convert to array for chart
      const categoryDataArray = Array.from(categorySales.entries()).map(
        ([category, data]) => ({
          category,
          amount: data.amount,
          count: data.count,
        })
      );

      setCategorySalesData(categoryDataArray);
    } catch (error) {
      console.error("Error fetching category sales:", error);
    }
  };

  const fetchPaymentMethodData = async () => {
    try {
      const startDateTime = new Date(`${startDate}T00:00:00`);
      const endDateTime = new Date(`${endDate}T23:59:59`);

      // Get sales by payment method using the client DB
      const paymentMethodData =
        await clientDb.transactions.getSalesByPaymentMethod(
          startDateTime,
          endDateTime
        );

      if (!paymentMethodData || !Array.isArray(paymentMethodData)) {
        console.warn("No payment method data returned");
        setPaymentMethodData([]);
        return;
      }

      // Convert to the format expected by the UI
      const paymentDataArray = paymentMethodData.map((item: any) => ({
        method: item.method || "unknown",
        amount: item.totalAmount || 0,
        count: item.transactionCount || 0,
      }));

      setPaymentMethodData(paymentDataArray);
    } catch (error) {
      console.error("Error fetching payment method data:", error);
    }
  };

  const fetchTopProducts = async () => {
    try {
      const startDateTime = new Date(`${startDate}T00:00:00`);
      const endDateTime = new Date(`${endDate}T23:59:59`);

      // Get top selling products using the client DB
      const topProductsData = await clientDb.transactions.getTopSellingProducts(
        startDateTime,
        endDateTime,
        5 // Limit to top 5 products
      );

      if (!topProductsData || !Array.isArray(topProductsData)) {
        console.warn("No top products data returned");
        setTopProductsData([]);
        return;
      }

      // Convert to the format expected by the UI and calculate profit
      const topProducts = topProductsData.map((item: any) => {
        const totalAmount = item.totalAmount || 0;
        const totalQuantity = item.totalQuantity || 0;
        const purchasePrice = item.purchasePrice || 0;
        const totalCost = purchasePrice * totalQuantity;
        const profit = totalAmount - totalCost;
        const profitMargin = totalAmount > 0 ? (profit / totalAmount) * 100 : 0;

        return {
          product: item.productName || "Unknown Product",
          quantity: totalQuantity,
          amount: totalAmount,
          profit: profit,
          profitMargin: profitMargin,
        };
      });

      setTopProductsData(topProducts);
    } catch (error) {
      console.error("Error fetching top products:", error);
    }
  };

  const fetchInventoryReport = async () => {
    try {
      // Get inventory data using the client DB
      const inventoryData = await clientDb.inventory.getAll();

      if (!inventoryData || !Array.isArray(inventoryData)) {
        console.warn("No inventory data returned");
        // Set placeholder data for now
        console.log("Inventory report not yet fully implemented");
        return;
      }

      // Get low stock items
      const lowStockItems = await fetch("/api/db/inventory?lowStock=true")
        .then((res) => res.json())
        .then((data) => data.inventory || [])
        .catch((error) => {
          console.error("Error fetching low stock items:", error);
          return [];
        });

      console.log("Inventory data fetched:", inventoryData.length, "items");
      console.log("Low stock items:", lowStockItems.length);

      // Here you would process the inventory data for charts and tables
      // For now, we're just logging it
    } catch (error) {
      console.error("Error fetching inventory report:", error);
    }
  };

  const fetchTransactionsReport = async () => {
    try {
      const startDateTime = new Date(`${startDate}T00:00:00`);
      const endDateTime = new Date(`${endDate}T23:59:59`);

      // Get transactions using the client DB
      const transactions = await clientDb.transactions.findByDateRange(
        startDateTime,
        endDateTime
      );

      if (!transactions || !Array.isArray(transactions)) {
        console.warn("No transactions data returned");
        setTransactionsData([]);
        return;
      }

      // Process transactions
      const processedTransactions = transactions.map((transaction: any) => ({
        id: transaction.id,
        date: format(new Date(transaction.createdAt), "yyyy-MM-dd HH:mm"),
        amount: transaction.finalAmount || 0,
        payment_method: transaction.paymentMethod || "unknown",
        status: transaction.status || "unknown",
      }));

      setTransactionsData(processedTransactions);
      setTotalTransactions(processedTransactions.length);
      setTotalSales(
        processedTransactions.reduce((sum, t) => sum + t.amount, 0)
      );
      setAverageOrderValue(
        processedTransactions.length > 0
          ? processedTransactions.reduce((sum, t) => sum + t.amount, 0) /
              processedTransactions.length
          : 0
      );
    } catch (error) {
      console.error("Error fetching transactions report:", error);
    }
  };

  const handleExport = () => {
    // This would export the current report data to CSV
    toast.info(t("reports.comingSoon"));
  };

  const handlePrint = () => {
    // This would print the current report
    window.print();
  };

  const formatPaymentMethod = (method: string) => {
    switch (method) {
      case "cash":
        return t("sales.cash");
      case "card":
        return t("sales.card");
      case "mobile":
        return t("sales.mobilePayment");
      default:
        return method;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">{t("reports.title")}</h1>
          <p className="text-muted-foreground">{t("reports.description")}</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={fetchReportData}>
            <RefreshCw className="h-4 w-4" />
            <span className="sr-only">{t("reports.refresh")}</span>
          </Button>
          <Button variant="outline" size="icon" onClick={handleExport}>
            <Download className="h-4 w-4" />
            <span className="sr-only">{t("reports.export")}</span>
          </Button>
          <Button variant="outline" size="icon" onClick={handlePrint}>
            <Printer className="h-4 w-4" />
            <span className="sr-only">{t("reports.print")}</span>
          </Button>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-4">
        <div className="w-full md:w-3/5">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>{t("reports.reportType")}</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs
                defaultValue="sales"
                value={reportType}
                onValueChange={(value) => setReportType(value as ReportType)}
              >
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="sales">
                    {t("reports.salesReport")}
                  </TabsTrigger>
                  <TabsTrigger value="inventory">
                    {t("reports.inventoryReport")}
                  </TabsTrigger>
                  <TabsTrigger value="transactions">
                    {t("reports.transactionReport")}
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        <div className="w-full md:w-2/5">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>{t("reports.dateRange")}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <Select
                    value={dateRange}
                    onValueChange={(value) => setDateRange(value as DateRange)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t("reports.selectDateRange")} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="today">
                        {t("reports.today")}
                      </SelectItem>
                      <SelectItem value="yesterday">
                        {t("reports.yesterday")}
                      </SelectItem>
                      <SelectItem value="week">
                        {t("reports.thisWeek")}
                      </SelectItem>
                      <SelectItem value="month">
                        {t("reports.thisMonth")}
                      </SelectItem>
                      <SelectItem value="custom">
                        {t("reports.custom")}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                {dateRange === "custom" && (
                  <>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <Input
                          type="date"
                          value={startDate}
                          onChange={(e) => setStartDate(e.target.value)}
                          className="flex-1"
                        />
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <Input
                          type="date"
                          value={endDate}
                          onChange={(e) => setEndDate(e.target.value)}
                          className="flex-1"
                        />
                      </div>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <p>{t("reports.loadingData")}</p>
        </div>
      ) : (
        <>
          {/* Summary Cards */}
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {t("reports.totalSales")}
                </CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(totalSales)}
                </div>
                <p className="text-xs text-muted-foreground">
                  {format(parseISO(startDate), "PPP")} -{" "}
                  {format(parseISO(endDate), "PPP")}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {t("reports.totalProfit")}
                </CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {formatCurrency(totalProfit)}
                </div>
                <p className="text-xs text-muted-foreground">
                  {profitMargin.toFixed(2)}% {t("reports.profitMargin")}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {t("reports.totalTransactions")}
                </CardTitle>
                <ShoppingCart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{totalTransactions}</div>
                <p className="text-xs text-muted-foreground">
                  {format(parseISO(startDate), "PPP")} -{" "}
                  {format(parseISO(endDate), "PPP")}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {t("reports.averageOrderValue")}
                </CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(averageOrderValue)}
                </div>
                <p className="text-xs text-muted-foreground">
                  {format(parseISO(startDate), "PPP")} -{" "}
                  {format(parseISO(endDate), "PPP")}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Report Content */}
          {reportType === "sales" && (
            <div className="space-y-6">
              {/* Sales Over Time Chart */}
              <Card>
                <CardHeader>
                  <CardTitle>{t("reports.salesOverTime")}</CardTitle>
                  <CardDescription>
                    {format(parseISO(startDate), "PPP")} -{" "}
                    {format(parseISO(endDate), "PPP")}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    {salesData.length > 0 ? (
                      <div className="h-full">
                        <Line
                          data={{
                            labels: salesData.map((item) =>
                              format(parseISO(item.date), "MMM dd")
                            ),
                            datasets: [
                              {
                                label: t("reports.amount"),
                                data: salesData.map((item) => item.amount),
                                borderColor: CHART_COLORS.primary,
                                backgroundColor: CHART_COLORS.primaryLight,
                                fill: true,
                                pointBackgroundColor: CHART_COLORS.primary,
                                pointBorderColor: "#fff",
                                pointBorderWidth: 2,
                                pointHoverBackgroundColor: "#fff",
                                pointHoverBorderColor: CHART_COLORS.primary,
                                pointHoverBorderWidth: 3,
                                pointHoverRadius: 6,
                                yAxisID: "y",
                              },
                              {
                                label: t("reports.profit"),
                                data: salesData.map((item) => item.profit),
                                borderColor: CHART_COLORS.success,
                                backgroundColor: CHART_COLORS.successLight,
                                fill: true,
                                pointBackgroundColor: CHART_COLORS.success,
                                pointBorderColor: "#fff",
                                pointBorderWidth: 2,
                                pointHoverBackgroundColor: "#fff",
                                pointHoverBorderColor: CHART_COLORS.success,
                                pointHoverBorderWidth: 3,
                                pointHoverRadius: 6,
                                yAxisID: "y",
                              },
                              {
                                label: t("reports.transactions"),
                                data: salesData.map(
                                  (item) => item.transactions
                                ),
                                borderColor: CHART_COLORS.secondary,
                                backgroundColor: CHART_COLORS.secondaryLight,
                                borderDash: [],
                                fill: false,
                                pointBackgroundColor: CHART_COLORS.secondary,
                                pointBorderColor: "#fff",
                                pointBorderWidth: 2,
                                pointHoverBackgroundColor: "#fff",
                                pointHoverBorderColor: CHART_COLORS.secondary,
                                pointHoverBorderWidth: 3,
                                pointHoverRadius: 6,
                                yAxisID: "y1",
                              },
                            ],
                          }}
                          options={{
                            ...chartOptions,
                            plugins: {
                              ...chartOptions.plugins,
                              tooltip: {
                                ...chartOptions.plugins.tooltip,
                                callbacks: {
                                  ...chartOptions.plugins.tooltip.callbacks,
                                  label: function (context) {
                                    let label = context.dataset.label || "";
                                    if (label) {
                                      label += ": ";
                                    }
                                    if (context.parsed.y !== null) {
                                      if (context.datasetIndex === 0) {
                                        label += formatCurrency(
                                          context.parsed.y
                                        );
                                      } else {
                                        label += context.parsed.y;
                                      }
                                    }
                                    return label;
                                  },
                                },
                              },
                            },
                            scales: {
                              ...chartOptions.scales,
                              y: {
                                type: "linear",
                                display: true,
                                position: "left",
                                title: {
                                  display: true,
                                  text: t("reports.amount"),
                                  font: {
                                    size: 12,
                                  },
                                },
                                grid: {
                                  borderDash: [3, 3],
                                  color: "rgba(0, 0, 0, 0.1)",
                                },
                                ticks: {
                                  callback: function (value) {
                                    return formatCurrency(value as number);
                                  },
                                },
                              },
                              y1: {
                                type: "linear",
                                display: true,
                                position: "right",
                                title: {
                                  display: true,
                                  text: t("reports.transactions"),
                                  font: {
                                    size: 12,
                                  },
                                },
                                grid: {
                                  drawOnChartArea: false,
                                },
                              },
                            },
                          }}
                        />
                      </div>
                    ) : (
                      <div className="flex justify-center items-center h-full">
                        <p>{t("reports.noData")}</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Sales by Category & Payment Method */}
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>{t("reports.salesByCategory")}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-64">
                      {categorySalesData.length > 0 ? (
                        <div className="h-full">
                          <Doughnut
                            data={{
                              labels: categorySalesData.map(
                                (item) => item.category
                              ),
                              datasets: [
                                {
                                  data: categorySalesData.map(
                                    (item) => item.amount
                                  ),
                                  backgroundColor: [
                                    CHART_COLORS.primary,
                                    CHART_COLORS.secondary,
                                    CHART_COLORS.success,
                                    CHART_COLORS.warning,
                                    CHART_COLORS.info,
                                    CHART_COLORS.purple,
                                    CHART_COLORS.pink,
                                    CHART_COLORS.red,
                                  ],
                                  borderColor: "#fff",
                                  borderWidth: 2,
                                  hoverOffset: 10,
                                  hoverBorderWidth: 3,
                                },
                              ],
                            }}
                            options={{
                              ...chartOptions,
                              cutout: "60%",
                              plugins: {
                                ...chartOptions.plugins,
                                tooltip: {
                                  ...chartOptions.plugins.tooltip,
                                  callbacks: {
                                    ...chartOptions.plugins.tooltip.callbacks,
                                    label: function (context) {
                                      const label = context.label || "";
                                      const value = context.raw as number;
                                      const total = context.dataset.data.reduce(
                                        (a: number, b: number) => a + b,
                                        0
                                      ) as number;
                                      const percentage = Math.round(
                                        (value / total) * 100
                                      );
                                      return `${label}: ${formatCurrency(
                                        value
                                      )} (${percentage}%)`;
                                    },
                                  },
                                },
                                legend: {
                                  ...chartOptions.plugins.legend,
                                  position: "right",
                                  labels: {
                                    ...chartOptions.plugins.legend.labels,
                                    generateLabels: function (chart) {
                                      // Get the default labels
                                      const original =
                                        ChartLegend.defaults.labels.generateLabels(
                                          chart
                                        );

                                      // Modify the labels to include percentages
                                      const total =
                                        chart.data.datasets[0].data.reduce(
                                          (a: number, b: number) => a + b,
                                          0
                                        ) as number;

                                      return original.map((label, i) => {
                                        const value = chart.data.datasets[0]
                                          .data[i] as number;
                                        const percentage = Math.round(
                                          (value / total) * 100
                                        );
                                        label.text = `${label.text} (${percentage}%)`;
                                        return label;
                                      });
                                    },
                                  },
                                },
                                datalabels: {
                                  display: function (context) {
                                    const value = context.dataset.data[
                                      context.dataIndex
                                    ] as number;
                                    const total = context.dataset.data.reduce(
                                      (a: number, b: number) => a + b,
                                      0
                                    ) as number;
                                    const percentage = (value / total) * 100;
                                    return percentage > 5; // Only show labels for slices > 5%
                                  },
                                  formatter: function (value, context) {
                                    const total = context.dataset.data.reduce(
                                      (a: number, b: number) => a + b,
                                      0
                                    ) as number;
                                    const percentage = Math.round(
                                      (value / total) * 100
                                    );
                                    return `${percentage}%`;
                                  },
                                  color: "#fff",
                                  font: {
                                    weight: "bold",
                                    size: 12,
                                  },
                                },
                              },
                            }}
                          />
                        </div>
                      ) : (
                        <div className="flex justify-center items-center h-full">
                          <p>{t("reports.noData")}</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>{t("reports.salesByPaymentMethod")}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-64">
                      {paymentMethodData.length > 0 ? (
                        <div className="h-full">
                          <Bar
                            data={{
                              labels: paymentMethodData.map((item) =>
                                formatPaymentMethod(item.method)
                              ),
                              datasets: [
                                {
                                  label: t("reports.amount"),
                                  data: paymentMethodData.map(
                                    (item) => item.amount
                                  ),
                                  backgroundColor: CHART_COLORS.success,
                                  borderColor: CHART_COLORS.success,
                                  borderWidth: 1,
                                  borderRadius: 4,
                                  yAxisID: "y",
                                },
                                {
                                  label: t("reports.transactions"),
                                  data: paymentMethodData.map(
                                    (item) => item.count
                                  ),
                                  backgroundColor: CHART_COLORS.warning,
                                  borderColor: CHART_COLORS.warning,
                                  borderWidth: 1,
                                  borderRadius: 4,
                                  yAxisID: "y1",
                                },
                              ],
                            }}
                            options={{
                              ...chartOptions,
                              indexAxis: "y",
                              plugins: {
                                ...chartOptions.plugins,
                                tooltip: {
                                  ...chartOptions.plugins.tooltip,
                                  callbacks: {
                                    ...chartOptions.plugins.tooltip.callbacks,
                                    label: function (context) {
                                      let label = context.dataset.label || "";
                                      if (label) {
                                        label += ": ";
                                      }
                                      if (context.parsed.x !== null) {
                                        if (context.datasetIndex === 0) {
                                          label += formatCurrency(
                                            context.parsed.x
                                          );
                                        } else {
                                          label += context.parsed.x;
                                        }
                                      }
                                      return label;
                                    },
                                  },
                                },
                              },
                              scales: {
                                x: {
                                  position: "top",
                                  grid: {
                                    borderDash: [3, 3],
                                    color: "rgba(0, 0, 0, 0.1)",
                                  },
                                },
                                y: {
                                  type: "linear",
                                  display: true,
                                  position: "left",
                                  title: {
                                    display: true,
                                    text: t("reports.amount"),
                                    font: {
                                      size: 12,
                                    },
                                  },
                                  ticks: {
                                    callback: function (value) {
                                      return formatCurrency(value as number);
                                    },
                                  },
                                },
                                y1: {
                                  type: "linear",
                                  display: true,
                                  position: "right",
                                  title: {
                                    display: true,
                                    text: t("reports.transactions"),
                                    font: {
                                      size: 12,
                                    },
                                  },
                                  grid: {
                                    drawOnChartArea: false,
                                  },
                                },
                              },
                            }}
                          />
                        </div>
                      ) : (
                        <div className="flex justify-center items-center h-full">
                          <p>{t("reports.noData")}</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Top Selling Products */}
              <Card>
                <CardHeader>
                  <CardTitle>{t("reports.topSellingProducts")}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    {topProductsData.length > 0 ? (
                      <div className="h-full">
                        <Bar
                          data={{
                            labels: topProductsData.map((item) =>
                              item.product.length > 20
                                ? item.product.substring(0, 18) + "..."
                                : item.product
                            ),
                            datasets: [
                              {
                                label: t("reports.quantity"),
                                data: topProductsData.map(
                                  (item) => item.quantity
                                ),
                                backgroundColor: CHART_COLORS.info,
                                borderColor: CHART_COLORS.info,
                                borderWidth: 1,
                                borderRadius: 4,
                                yAxisID: "y",
                              },
                              {
                                label: t("reports.amount"),
                                data: topProductsData.map(
                                  (item) => item.amount
                                ),
                                backgroundColor: CHART_COLORS.purple,
                                borderColor: CHART_COLORS.purple,
                                borderWidth: 1,
                                borderRadius: 4,
                                yAxisID: "y1",
                              },
                              {
                                label: t("reports.profit"),
                                data: topProductsData.map(
                                  (item) => item.profit
                                ),
                                backgroundColor: CHART_COLORS.success,
                                borderColor: CHART_COLORS.success,
                                borderWidth: 1,
                                borderRadius: 4,
                                yAxisID: "y1",
                              },
                            ],
                          }}
                          options={{
                            ...chartOptions,
                            indexAxis: "y",
                            plugins: {
                              ...chartOptions.plugins,
                              tooltip: {
                                ...chartOptions.plugins.tooltip,
                                callbacks: {
                                  ...chartOptions.plugins.tooltip.callbacks,
                                  title: function (tooltipItems) {
                                    // Get the original product name (not truncated)
                                    const index = tooltipItems[0].dataIndex;
                                    return topProductsData[index].product;
                                  },
                                  label: function (context) {
                                    let label = context.dataset.label || "";
                                    if (label) {
                                      label += ": ";
                                    }
                                    if (context.parsed.x !== null) {
                                      if (
                                        context.datasetIndex === 1 ||
                                        context.datasetIndex === 2
                                      ) {
                                        label += formatCurrency(
                                          context.parsed.x
                                        );

                                        // Add profit margin for profit dataset
                                        if (context.datasetIndex === 2) {
                                          const index = context.dataIndex;
                                          const profitMargin =
                                            topProductsData[index].profitMargin;
                                          label += ` (${profitMargin.toFixed(
                                            2
                                          )}%)`;
                                        }
                                      } else {
                                        label += context.parsed.x;
                                      }
                                    }
                                    return label;
                                  },
                                },
                              },
                            },
                            scales: {
                              x: {
                                grid: {
                                  borderDash: [3, 3],
                                  color: "rgba(0, 0, 0, 0.1)",
                                },
                                ticks: {
                                  font: {
                                    size: 12,
                                  },
                                },
                              },
                              y: {
                                grid: {
                                  display: false,
                                },
                                ticks: {
                                  font: {
                                    size: 12,
                                  },
                                  // Limit the number of ticks to avoid crowding
                                  maxTicksLimit: 8,
                                },
                              },
                              y1: {
                                position: "right",
                                grid: {
                                  display: false,
                                },
                                ticks: {
                                  callback: function (value) {
                                    return formatCurrency(value as number);
                                  },
                                },
                              },
                            },
                          }}
                        />
                      </div>
                    ) : (
                      <div className="flex justify-center items-center h-full">
                        <p>{t("reports.noData")}</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {reportType === "inventory" && (
            <div className="flex justify-center items-center h-64">
              <p>{t("reports.comingSoon")}</p>
            </div>
          )}

          {reportType === "transactions" && (
            <Card>
              <CardHeader>
                <CardTitle>{t("reports.transactionDetails")}</CardTitle>
                <CardDescription>
                  {format(parseISO(startDate), "PPP")} -{" "}
                  {format(parseISO(endDate), "PPP")}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {transactionsData.length > 0 ? (
                  <div className="rounded-md border">
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead>
                          <tr className="bg-muted/50">
                            <th className="p-2 text-left font-medium">
                              {t("reports.id")}
                            </th>
                            <th className="p-2 text-left font-medium">
                              {t("reports.date")}
                            </th>
                            <th className="p-2 text-left font-medium">
                              {t("reports.amount")}
                            </th>
                            <th className="p-2 text-left font-medium">
                              {t("reports.paymentMethod")}
                            </th>
                            <th className="p-2 text-left font-medium">
                              {t("reports.status")}
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {transactionsData.map((transaction) => (
                            <tr key={transaction.id} className="border-t">
                              <td className="p-2">
                                {transaction.id.substring(0, 8)}
                              </td>
                              <td className="p-2">{transaction.date}</td>
                              <td className="p-2">
                                {formatCurrency(transaction.amount)}
                              </td>
                              <td className="p-2">
                                {formatPaymentMethod(
                                  transaction.payment_method
                                )}
                              </td>
                              <td className="p-2">{transaction.status}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                ) : (
                  <div className="flex justify-center items-center h-64">
                    <p>{t("reports.noData")}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  );
}
