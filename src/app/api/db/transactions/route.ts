import { NextRequest, NextResponse } from "next/server";

// Import the database with error handling
let db: any;
try {
  const dbModule = require("@/lib/database");
  db = dbModule.db;
} catch (error) {
  console.error("Failed to import database module:", error);
}

export async function GET(request: NextRequest) {
  try {
    // Check if database is available
    if (!db) {
      return NextResponse.json(
        {
          error:
            "Database not initialized. Please run 'npx prisma generate' and restart the server.",
        },
        { status: 500 }
      );
    }

    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get("id");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    const recent = searchParams.get("recent");
    const limit = searchParams.get("limit");
    const salesByCategory = searchParams.get("salesByCategory");
    const salesByPaymentMethod = searchParams.get("salesByPaymentMethod");
    const topSellingProducts = searchParams.get("topSellingProducts");

    try {
      if (id) {
        // Get transaction by ID
        console.log(`Fetching transaction by ID: ${id}`);
        const transaction = await db.transactions.findById(id);
        return NextResponse.json({ transaction });
      } else if (startDate && endDate && salesByCategory === "true") {
        // Get sales by category
        console.log(
          `Fetching sales by category from ${startDate} to ${endDate}`
        );
        const rawSales = await db.transactions.getSalesByCategory(
          new Date(startDate),
          new Date(endDate)
        );

        // Convert BigInt values to numbers
        const sales = rawSales.map((item: any) => ({
          categoryId: item.categoryId,
          categoryName: item.categoryName,
          totalAmount: Number(item.totalAmount),
          itemCount: Number(item.itemCount),
        }));

        return NextResponse.json({ sales });
      } else if (startDate && endDate && salesByPaymentMethod === "true") {
        // Get sales by payment method
        console.log(
          `Fetching sales by payment method from ${startDate} to ${endDate}`
        );
        const rawSales = await db.transactions.getSalesByPaymentMethod(
          new Date(startDate),
          new Date(endDate)
        );

        // Convert BigInt values to numbers
        const sales = rawSales.map((item: any) => ({
          method: item.method,
          totalAmount: Number(item.totalAmount),
          transactionCount: Number(item.transactionCount),
        }));

        return NextResponse.json({ sales });
      } else if (startDate && endDate && topSellingProducts === "true") {
        // Get top selling products
        console.log(
          `Fetching top selling products from ${startDate} to ${endDate}`
        );
        const rawProducts = await db.transactions.getTopSellingProducts(
          new Date(startDate),
          new Date(endDate),
          limit ? parseInt(limit) : undefined
        );

        // Convert BigInt values to numbers
        const products = rawProducts.map((item: any) => ({
          productId: item.productId,
          productName: item.productName,
          purchasePrice: Number(item.purchasePrice || 0),
          totalQuantity: Number(item.totalQuantity),
          totalAmount: Number(item.totalAmount),
        }));

        return NextResponse.json({ products });
      } else if (startDate && endDate) {
        // Get transactions by date range
        console.log(`Fetching transactions from ${startDate} to ${endDate}`);
        const transactions = await db.transactions.findByDateRange(
          new Date(startDate),
          new Date(endDate)
        );
        return NextResponse.json({ transactions });
      } else if (recent === "true") {
        // Get recent transactions
        console.log(`Fetching recent transactions, limit: ${limit || 10}`);
        const transactions = await db.transactions.getRecentTransactions(
          limit ? parseInt(limit) : undefined
        );
        return NextResponse.json({ transactions });
      } else {
        // Get all transactions
        console.log("Fetching all transactions");
        const transactions = await db.transactions.findAll();
        return NextResponse.json({ transactions });
      }
    } catch (error: any) {
      console.error("Error fetching transactions:", error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
  } catch (error: any) {
    console.error("API error:", error);
    return NextResponse.json(
      { error: error.message || "An unknown error occurred" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check if database is available
    if (!db) {
      return NextResponse.json(
        {
          error:
            "Database not initialized. Please run 'npx prisma generate' and restart the server.",
        },
        { status: 500 }
      );
    }

    // Parse request body
    let data;
    try {
      data = await request.json();
      console.log("Transaction data received:", data);
    } catch (error) {
      console.error("Error parsing request body:", error);
      return NextResponse.json(
        { error: "Invalid JSON in request body" },
        { status: 400 }
      );
    }

    // Validate required fields
    if (!data.user_id) {
      return NextResponse.json(
        { error: "User ID is required" },
        { status: 400 }
      );
    }
    if (data.total_amount === undefined) {
      return NextResponse.json(
        { error: "Total amount is required" },
        { status: 400 }
      );
    }
    if (data.final_amount === undefined) {
      return NextResponse.json(
        { error: "Final amount is required" },
        { status: 400 }
      );
    }
    if (!data.payment_method) {
      return NextResponse.json(
        { error: "Payment method is required" },
        { status: 400 }
      );
    }
    if (!data.status) {
      return NextResponse.json(
        { error: "Status is required" },
        { status: 400 }
      );
    }

    // Convert snake_case to camelCase
    try {
      // Convert items from snake_case to camelCase
      const items = data.items
        ? data.items.map((item) => ({
            productId: item.product_id,
            quantity: item.quantity,
            unitPrice: item.unit_price,
            totalPrice: item.total_price,
            discount: item.discount,
            discountType: item.discount_type,
          }))
        : [];

      console.log("Converted items:", items);

      // Process customer info if provided
      const customerInfo = data.customer_info || null;
      console.log("Customer info:", customerInfo);

      // Validate items
      if (!items || items.length === 0) {
        console.error("No items provided for transaction");
        return NextResponse.json(
          { error: "No items provided for transaction" },
          { status: 400 }
        );
      }

      // Validate each item has required fields
      for (const item of items) {
        if (!item.productId) {
          console.error("Missing productId in item:", item);
          return NextResponse.json(
            { error: "Missing productId in transaction item" },
            { status: 400 }
          );
        }
        if (item.quantity === undefined) {
          console.error("Missing quantity in item:", item);
          return NextResponse.json(
            { error: "Missing quantity in transaction item" },
            { status: 400 }
          );
        }
        if (item.unitPrice === undefined) {
          console.error("Missing unitPrice in item:", item);
          return NextResponse.json(
            { error: "Missing unitPrice in transaction item" },
            { status: 400 }
          );
        }
        if (item.totalPrice === undefined) {
          console.error("Missing totalPrice in item:", item);
          return NextResponse.json(
            { error: "Missing totalPrice in transaction item" },
            { status: 400 }
          );
        }
      }

      try {
        // Create transaction with customerInfo field
        const transaction = await db.transactions.create({
          userId: data.user_id,
          totalAmount: data.total_amount,
          discountAmount: data.discount_amount || 0,
          finalAmount: data.final_amount,
          paymentMethod: data.payment_method,
          status: data.status,
          items: items,
          customerInfo: customerInfo,
        });

        console.log("Transaction created:", transaction);
        return NextResponse.json({ transaction });
      } catch (dbError: any) {
        console.error("Database error creating transaction:", dbError);
        return NextResponse.json(
          {
            error: `Database error: ${dbError.message}`,
            details: dbError.stack,
          },
          { status: 500 }
        );
      }
    } catch (error: any) {
      console.error("Transaction creation error:", error);
      return NextResponse.json(
        {
          error: `Transaction creation error: ${error.message}`,
          details: error.stack,
        },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error("API error:", error);
    return NextResponse.json(
      { error: error.message || "An unknown error occurred" },
      { status: 500 }
    );
  }
}
