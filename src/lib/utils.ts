import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Format currency based on locale and TND
export function formatCurrency(amount: number | undefined | null): string {
  if (amount === undefined || amount === null) {
    return "0.00 TND";
  }

  try {
    return new Intl.NumberFormat("en", {
      style: "currency",
      currency: "TND",
      currencyDisplay: "symbol",
    }).format(amount);
  } catch (error) {
    console.error("Error formatting currency:", error);
    return `${amount.toFixed(2)} TND`;
  }
}

// Convert a file to a base64 string
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });
}
