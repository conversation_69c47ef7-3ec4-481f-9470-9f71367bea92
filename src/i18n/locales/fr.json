{"common": {"language": "<PERSON><PERSON>", "english": "<PERSON><PERSON><PERSON>", "french": "Français", "arabic": "<PERSON><PERSON>", "currency": "TND", "refresh": "Actualiser", "dateRange": "Sélectionner une période", "notAvailable": "N/D"}, "auth": {"login": "Connexion", "register": "S'inscrire", "email": "Email", "password": "Mot de passe", "confirmPassword": "Confirmer le mot de passe", "forgotPassword": "Mot de passe oublié?", "noAccount": "Vous n'avez pas de compte?", "haveAccount": "Vous avez déjà un compte?", "alreadyHaveAccount": "Vous avez déjà un compte?", "signIn": "Se connecter", "signUp": "S'inscrire", "logout": "Déconnexion", "loginSuccessful": "Connexion réussie", "loginFailed": "Échec de la connexion", "registrationSuccessful": "Inscription réussie", "registrationFailed": "Échec de l'inscription", "checkEmail": "Veuillez vérifier votre email pour les instructions de vérification", "createAccount": "<PERSON><PERSON><PERSON> un compte", "enterEmailPassword": "Entrez votre email et mot de passe pour créer votre compte", "creatingAccount": "Création du compte...", "signingIn": "Connexion en cours..."}, "dashboard": {"dashboard": "Tableau de bord", "overview": "Aperçu de votre entreprise", "totalSales": "Ventes totales", "lifetimeSales": "Montant total des ventes", "products": "Produits", "totalProducts": "Total des produits en stock", "transactions": "Transactions", "totalTransactions": "Total des transactions complétées", "lowStockItems": "Articles en stock faible", "itemsBelowThreshold": "Articles sous le seuil", "recentTransactions": "Transactions récentes", "recentTransactionsDesc": "Vos transactions de vente les plus récentes", "noRecentTransactions": "Aucune transaction récente trouvée.", "lowStockAlerts": "Alertes de stock faible", "lowStockAlertsDesc": "Produits qui doivent être réapprovisionnés", "noLowStockItems": "Aucun article en stock faible trouvé.", "id": "ID", "amount": "<PERSON><PERSON>", "paymentMethod": "Méthode de paiement", "date": "Date", "product": "Produit", "quantity": "Quantité", "threshold": "<PERSON><PERSON>"}, "products": {"products": "Produits", "manageProducts": "G<PERSON>rer votre inventaire de produits", "addProduct": "Ajouter un produit", "editProduct": "Modifier le produit", "deleteProduct": "Supprimer le produit", "name": "Nom", "price": "Prix", "category": "<PERSON><PERSON><PERSON><PERSON>", "barcode": "Code-barres", "quantity": "Quantité", "status": "Statut", "actions": "Actions", "description": "Description", "lowStockThreshold": "Seuil de stock faible", "inStock": "En stock", "outOfStock": "Rupture de stock", "lowStock": "Stock faible", "addNewProduct": "Ajouter un nouveau produit", "addNewProductDesc": "Entrez les détails du nouveau produit.", "editProductDesc": "Mettre à jour les détails de ce produit.", "confirmDeletion": "Confirmer la <PERSON>", "confirmDeletionDesc": "Êtes-vous sûr de vouloir supprimer ce produit? Cette action ne peut pas être annulée.", "cancel": "Annuler", "update": "Mettre à jour le produit", "add": "Ajouter", "delete": "<PERSON><PERSON><PERSON><PERSON>", "searchProducts": "Rechercher des produits par nom ou code-barres...", "loadingProducts": "Chargement des produits...", "noProductsFound": "Aucun produit trouvé.", "productAddedSuccess": "Produit ajouté avec succès", "productAddedError": "Échec de l'ajout du produit", "productUpdatedSuccess": "Produit mis à jour avec succès", "productUpdatedError": "Échec de la mise à jour du produit", "productDeletedSuccess": "Produit supprimé avec succès", "productDeletedError": "Échec de la suppression du produit", "product": "Produit", "purchasePrice": "Prix d'achat", "supplier": "Fournisseur", "supplierName": "Nom du fournisseur", "productImage": "Image du produit", "dragAndDrop": "Téléchargez une image ou glissez-d<PERSON><PERSON>z"}, "sales": {"salesTerminal": "Terminal de vente", "processSales": "Traiter les transactions et gérer les ventes", "searchProducts": "Rechercher produits", "shoppingCart": "<PERSON><PERSON>", "itemsInCart": "{count, plural, =0 {Aucun article} =1 {1 article} other {# articles}} dans le panier", "emptyCart": "Votre panier est vide", "item": "Article", "qty": "Qté", "price": "Prix", "actions": "Actions", "subtotal": "Sous-total", "discount": "Remise", "total": "Total", "clearCart": "Vider le panier", "checkout": "Paiement", "completeTransaction": "Complétez les détails de la transaction.", "paymentMethod": "Méthode de paiement", "cash": "Espèces", "card": "<PERSON><PERSON>", "mobilePayment": "Paiement mobile", "discountAmount": "<PERSON><PERSON> de <PERSON> re<PERSON>", "completeSale": "Finaliser la vente", "receipt": "<PERSON><PERSON><PERSON>", "transactionSuccess": "Transaction complétée avec succès", "close": "<PERSON><PERSON><PERSON>", "printReceipt": "Imprimer le reçu", "thankYou": "Merci pour votre achat!", "productNotFound": "Produit non trouvé", "outOfStock": "Produit en rupture de stock", "notEnoughStock": "Stock insuffisant", "cartEmpty": "Le panier est vide", "loginRequired": "Vous devez être connecté pour effectuer une transaction", "transactionError": "Échec de la transaction", "added": "Ajouté: {name}", "allCategories": "Toutes les catégories", "filterByCategory": "Filtrer par catégorie", "category": "<PERSON><PERSON><PERSON><PERSON>", "amountReceived": "<PERSON><PERSON>", "skip": "<PERSON><PERSON><PERSON>", "change": "Monnaie", "insufficientAmountReceived": "Le montant reçu doit être au moins égal au montant total", "paperSize": "<PERSON><PERSON>apier", "printError": "Erreur d'impression du reçu", "remove": "<PERSON><PERSON><PERSON>", "quickActions": "Actions rapides", "quantity": "Quantité", "enterQuantity": "Entrer la quantité", "confirm": "Confirmer", "applyDiscount": "Appliquer une remise", "fixedAmount": "Montant fixe", "percentage": "Pourcentage", "discountPercentage": "Pourcentage de remise", "finalTotal": "Total final", "productDiscount": "Remise sur produit", "finalPrice": "Prix final", "removeDiscount": "Supprimer la remise", "discountedPrice": "Prix remisé", "itemDiscounts": "Remises sur articles", "orderDiscount": "Remise sur commande", "voidLastItem": "<PERSON><PERSON><PERSON> der<PERSON> article", "quickCheckout": "Paiement rapide", "holdOrder": "Mettre en attente", "recallOrder": "<PERSON><PERSON><PERSON> commande", "customerInfo": "Infos client", "orderHeld": "Commande mise en attente", "orderHeldDescription": "La commande {id} a été mise en attente. Vous avez maintenant {count} commande(s) en attente.", "orderRecalled": "Commande rappelée", "orderRecalledDescription": "La commande {id} a été rappelée", "multipleOrdersAvailable": "Commande {id} rappelée. {count} autres commandes en attente disponibles.", "noReceiptToPrint": "Pas de reçu à imprimer", "itemVoided": "Article annulé", "itemVoidedDescription": "{name} a été retiré du panier", "discountSummary": "Résumé des remises", "customerName": "Nom du client", "customerPhone": "Numéro de téléphone", "customerEmail": "Email", "enterCustomerName": "Entrez le nom du client", "enterCustomerPhone": "Entrez le numéro de téléphone", "enterCustomerEmail": "Entrez l'adresse email", "customerInfoSaved": "Informations client enregistrées", "cashier": "Cais<PERSON>r", "servedBy": "Servi par"}, "categories": {"categories": "Catégories", "manageCategories": "Gérer vos catégories de produits", "addCategory": "Ajouter une catégorie", "editCategory": "Modifier la catégorie", "deleteCategory": "Supprimer la catégorie", "name": "Nom", "description": "Description", "actions": "Actions", "selectCategory": "Sélectionner une catégorie", "uncategorized": "Sans catégorie", "addNewCategory": "Ajouter une nouvelle catégorie", "addNewCategoryDesc": "Entrez les détails de la nouvelle catégorie.", "editCategoryDesc": "Mettre à jour les détails de cette catégorie.", "confirmDeletion": "Confirmer la <PERSON>", "confirmDeletionDesc": "Êtes-vous sûr de vouloir supprimer cette catégorie? Cette action ne peut pas être annulée.", "cancel": "Annuler", "update": "Mettre à jour la catégorie", "add": "Ajouter", "delete": "<PERSON><PERSON><PERSON><PERSON>", "searchCategories": "Rechercher des catégories...", "loadingCategories": "Chargement des catégories...", "noCategoriesFound": "Aucune catégorie trouvée.", "categoryAddedSuccess": "Catégorie a<PERSON> avec succès", "categoryAddedError": "Échec de l'ajout de la catégorie", "categoryUpdatedSuccess": "Catégorie mise à jour avec succès", "categoryUpdatedError": "Échec de la mise à jour de la catégorie", "categoryDeletedSuccess": "Catégorie supprimée avec succès", "categoryDeletedError": "Échec de la suppression de la catégorie", "categoryInUse": "Catégorie en cours d'utilisation", "categoryInUseDesc": "Cette catégorie est utilisée par {count, plural, =1 {1 produit} other {# produits}}. Veuillez d'abord retirer la catégorie de ces produits."}, "layout": {"dashboard": "Tableau de bord", "products": "Produits", "categories": "Catégories", "sales": "<PERSON><PERSON><PERSON>", "reports": "Rapports", "settings": "Paramètres", "logout": "Déconnexion", "orders": "Commandes", "menu": "<PERSON><PERSON>"}, "menu": {"selectOption": "Sélectionner une option", "mainMenu": "<PERSON>u principal", "backToSales": "Retour aux ventes"}, "reports": {"title": "Rapports", "description": "Visualiser et analyser les données de votre entreprise", "salesReport": "Rapport des ventes", "inventoryReport": "Rapport d'inventaire", "transactionReport": "Rapport de transactions", "dateRange": "Période", "startDate": "Date de début", "endDate": "Date de fin", "filter": "<PERSON><PERSON><PERSON>", "export": "Exporter", "print": "<PERSON><PERSON><PERSON><PERSON>", "daily": "Quotidien", "weekly": "Hebdomadaire", "monthly": "<PERSON><PERSON><PERSON>", "yearly": "<PERSON><PERSON>", "custom": "<PERSON><PERSON><PERSON><PERSON>", "today": "<PERSON><PERSON><PERSON>'hui", "yesterday": "<PERSON>er", "thisWeek": "<PERSON><PERSON> se<PERSON>", "lastWeek": "<PERSON><PERSON><PERSON>", "thisMonth": "Ce mois-ci", "lastMonth": "<PERSON><PERSON>", "thisYear": "<PERSON><PERSON> an<PERSON>", "lastYear": "<PERSON><PERSON>", "allTime": "Tout le temps", "noData": "Aucune donnée disponible pour la période sélectionnée", "totalSales": "Ventes totales", "totalTransactions": "Transactions totales", "averageOrderValue": "Valeur moyenne des commandes", "topSellingProducts": "Produits les plus vendus", "salesByCategory": "Ventes par catégorie", "salesByPaymentMethod": "Ventes par méthode de paiement", "salesOverTime": "Évolution des ventes", "transactionDetails": "Détails de la transaction", "productName": "Nom du produit", "quantity": "Quantité", "amount": "<PERSON><PERSON>", "date": "Date", "time": "<PERSON><PERSON>", "status": "Statut", "category": "<PERSON><PERSON><PERSON><PERSON>", "loadingData": "Chargement des données...", "generateReport": "Générer le rapport", "reportType": "Type de rapport", "selectDateRange": "Sélectionner une période", "refresh": "Actualiser", "comingSoon": "Bientôt disponible"}, "settings": {"title": "Paramètres", "description": "G<PERSON>rer les paramètres de votre application", "general": "Général", "appearance": "Apparence", "notifications": "Notifications", "language": "<PERSON><PERSON>", "languageDescription": "Choisissez votre langue préférée pour l'application", "appearanceDescription": "Personnalisez l'apparence de l'application", "notificationsDescription": "<PERSON><PERSON><PERSON> vos préférences de notification", "saveLanguage": "Enregistrer la langue", "languageSaved": "Langue enregistrée avec succès", "comingSoon": "Bientôt disponible", "storeName": "Nom du magasin", "storeNameDescription": "Définissez le nom de votre magasin qui apparaîtra sur les reçus et le tableau de bord", "storeNamePlaceholder": "Entrez le nom de votre magasin", "saveStoreName": "Enregistrer le nom du magasin", "storeNameSaved": "Nom du magasin enregistré avec succès", "database": {"title": "Paramètres de la base de données", "description": "Gérer les paramètres de votre base de données locale", "information": "Informations de la base de données", "informationDescription": "Voir les informations sur votre base de données locale", "type": "Type de base de données", "location": "Emplacement de la base de données", "status": "État de la connexion", "connected": "Connecté", "version": "Version de la base de données", "backup": "Sauvegarder la base de données", "backupDescription": "<PERSON><PERSON>er une sauvegarde de votre base de données", "backupRestore": "Sauvegarde et restauration", "createBackup": "<PERSON><PERSON><PERSON> une sauvegarde", "backingUp": "Sauvegarde en cours...", "backupSuccess": "Sauvegarde de la base de données créée avec succès", "backupError": "Échec de la création de la sauvegarde de la base de données", "restore": "Restaurer la base de données", "restoreDescription": "Restaurer votre base de données à partir d'une sauvegarde", "restoreFromBackup": "Restaurer à partir d'une sauvegarde", "restoring": "Restauration en cours...", "restoreSuccess": "Base de données restaurée avec succès", "restoreError": "Échec de la restauration de la base de données", "reset": "Réinitialiser la base de données", "resetDescription": "Réinitialiser votre base de données à son état initial", "resetDatabase": "Réinitialiser la base de données", "resetConfirmation": "Êtes-vous sûr de vouloir réinitialiser la base de données? Cela supprimera toutes vos données et ne peut pas être annulé.", "resetSuccess": "Base de données réinitialisée avec succès", "resetError": "Échec de la réinitialisation de la base de données", "migration": "<PERSON><PERSON> de donn<PERSON>", "migrateFromSupabase": "Migrer depuis <PERSON>", "migrateFromSupabaseDescription": "Migrer vos données de Supabase vers la base de données locale", "startMigration": "<PERSON><PERSON><PERSON><PERSON>", "migrating": "Migration en cours...", "migrationSuccess": "Migration des données terminée avec succès", "migrationError": "Échec de la migration des données"}}, "orders": {"orderHistory": "Historique des commandes", "recentOrders": "Commandes récentes", "searchOrders": "Rechercher des commandes par ID, montant ou date...", "orderId": "ID de commande", "date": "Date", "amount": "<PERSON><PERSON>", "paymentMethod": "Méthode de paiement", "status": "Statut", "actions": "Actions", "noOrders": "Aucune commande trouvée", "noOrdersFound": "Aucune commande ne correspond à votre recherche", "orderDetails": "<PERSON><PERSON><PERSON> de la commande", "items": "Articles", "product": "Produit", "quantity": "Quantité", "unitPrice": "Prix unitaire", "total": "Total", "subtotal": "Sous-total", "discount": "Remise", "printReceipt": "Imprimer le reçu", "receipt": "<PERSON><PERSON><PERSON>", "print": "<PERSON><PERSON><PERSON><PERSON>"}, "errors": {"somethingWentWrong": "Une erreur s'est produite", "pleaseTryAgain": "<PERSON><PERSON><PERSON>z réessayer", "failedToLoadProducts": "Échec du chargement des produits", "failedToLoadCategories": "Échec du chargement des catégories", "failedToLoadDashboard": "Échec du chargement des données du tableau de bord", "productsTableNotFound": "Table de produits non trouvée. Veuillez configurer votre base de données Supabase.", "categoriesTableNotFound": "Table de catégories non trouvée. Veuillez configurer votre base de données Supabase.", "transactionsTableNotFound": "Table de transactions non trouvée. Veuillez configurer votre base de données Supabase.", "checkSupabaseSetup": "Veuillez vérifier votre configuration Supabase", "printError": "Échec de l'impression du reçu", "popupBlocked": "Fenêtre contextuelle bloquée. Veuillez autoriser les fenêtres contextuelles pour ce site."}}